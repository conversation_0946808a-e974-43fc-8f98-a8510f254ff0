<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OnlyMonster Revenue Tracker</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #333;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        .links-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .links-table th,
        .links-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        .links-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        .links-table tr:hover {
            background: #f8f9fa;
        }
        .revenue-input {
            width: 80px;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-align: right;
        }
        .update-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .update-btn:hover {
            background: #218838;
        }
        .conversion-rate {
            font-weight: bold;
        }
        .high-conversion {
            color: #28a745;
        }
        .medium-conversion {
            color: #ffc107;
        }
        .low-conversion {
            color: #dc3545;
        }
        .section {
            padding: 30px;
        }
        .section h2 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        .refresh-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 20px;
        }
        .refresh-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💰 OnlyMonster Revenue Tracker</h1>
            <p>Track and manage revenue for your OnlyMonster tracking links</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">{{ links|length }}</div>
                <div class="stat-label">Active Links</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">${{ "%.2f"|format(links|sum(attribute='revenue')) }}</div>
                <div class="stat-label">Total Revenue</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ links|sum(attribute='clicks')|int }}</div>
                <div class="stat-label">Total Clicks</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ links|sum(attribute='fans')|int }}</div>
                <div class="stat-label">Total Fans</div>
            </div>
        </div>

        <div class="section">
            <h2>📊 Tracking Links Revenue</h2>
            <button class="refresh-btn" onclick="location.reload()">🔄 Refresh Data</button>
            
            <table class="links-table">
                <thead>
                    <tr>
                        <th>Link Name</th>
                        <th>Clicks</th>
                        <th>Fans</th>
                        <th>Conversion %</th>
                        <th>Revenue</th>
                        <th>Update Revenue</th>
                        <th>Last Updated</th>
                    </tr>
                </thead>
                <tbody>
                    {% for link in links %}
                    <tr>
                        <td><strong>{{ link.name }}</strong></td>
                        <td>{{ link.clicks|int }}</td>
                        <td>{{ link.fans|int }}</td>
                        <td>
                            <span class="conversion-rate 
                                {% if link.conversion_rate >= 5 %}high-conversion
                                {% elif link.conversion_rate >= 2 %}medium-conversion
                                {% else %}low-conversion{% endif %}">
                                {{ "%.2f"|format(link.conversion_rate) }}%
                            </span>
                        </td>
                        <td><strong>${{ "%.2f"|format(link.revenue) }}</strong></td>
                        <td>
                            <input type="number" 
                                   class="revenue-input" 
                                   id="revenue-{{ loop.index }}"
                                   value="{{ link.revenue }}"
                                   step="0.01"
                                   min="0">
                            <button class="update-btn" 
                                    onclick="updateRevenue('{{ link.name }}', 'revenue-{{ loop.index }}')">
                                Update
                            </button>
                        </td>
                        <td>{{ link.timestamp }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <script>
        function updateRevenue(linkName, inputId) {
            const input = document.getElementById(inputId);
            const revenue = parseFloat(input.value) || 0;
            
            fetch('/update_revenue', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    link_name: linkName,
                    revenue: revenue
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Revenue updated successfully!');
                    location.reload();
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                alert('Error updating revenue: ' + error);
            });
        }
    </script>
</body>
</html>
