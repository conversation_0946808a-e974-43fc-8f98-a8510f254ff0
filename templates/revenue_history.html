<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Revenue History - {{ link_name }}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2em;
        }
        .back-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 15px;
            text-decoration: none;
            display: inline-block;
        }
        .back-btn:hover {
            background: rgba(255,255,255,0.3);
        }
        .history-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .history-table th,
        .history-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        .history-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        .history-table tr:hover {
            background: #f8f9fa;
        }
        .section {
            padding: 30px;
        }
        .section h2 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        .revenue-cell {
            font-weight: bold;
            color: #28a745;
        }
        .conversion-rate {
            font-weight: bold;
        }
        .high-conversion {
            color: #28a745;
        }
        .medium-conversion {
            color: #ffc107;
        }
        .low-conversion {
            color: #dc3545;
        }
        .stats-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #333;
        }
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📈 Revenue History</h1>
            <p><strong>{{ link_name }}</strong></p>
            <a href="/" class="back-btn">← Back to Dashboard</a>
        </div>

        <div class="section">
            <h2>📊 Summary Statistics</h2>
            <div class="stats-summary">
                <div class="stat-item">
                    <div class="stat-value">${{ "%.2f"|format(history|sum(attribute='revenue')) }}</div>
                    <div class="stat-label">Total Revenue</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${{ "%.2f"|format((history|sum(attribute='revenue')) / (history|length) if history|length > 0 else 0) }}</div>
                    <div class="stat-label">Avg Revenue</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{{ history|length }}</div>
                    <div class="stat-label">Data Points</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{{ "%.2f"|format((history|sum(attribute='conversion_rate')) / (history|length) if history|length > 0 else 0) }}%</div>
                    <div class="stat-label">Avg Conversion</div>
                </div>
            </div>

            <h2>📋 Historical Data</h2>
            {% if history %}
            <table class="history-table">
                <thead>
                    <tr>
                        <th>Date/Time</th>
                        <th>Clicks</th>
                        <th>Fans</th>
                        <th>Conversion %</th>
                        <th>Revenue</th>
                    </tr>
                </thead>
                <tbody>
                    {% for entry in history %}
                    <tr>
                        <td>{{ entry.timestamp }}</td>
                        <td>{{ entry.clicks|int }}</td>
                        <td>{{ entry.fans|int }}</td>
                        <td>
                            <span class="conversion-rate 
                                {% if entry.conversion_rate >= 5 %}high-conversion
                                {% elif entry.conversion_rate >= 2 %}medium-conversion
                                {% else %}low-conversion{% endif %}">
                                {{ "%.2f"|format(entry.conversion_rate) }}%
                            </span>
                        </td>
                        <td class="revenue-cell">${{ "%.2f"|format(entry.revenue) }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
            <p>No historical data available for this link.</p>
            {% endif %}
        </div>
    </div>
</body>
</html>
