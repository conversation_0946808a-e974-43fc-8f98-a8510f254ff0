"""
OnlyMonster.ai Tracking Links Scraper
Automates login and data extraction from tracking links page
"""
import time
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from database import TrackingDatabase
from analytics import TrackingAnalytics
from slack_webhook import SlackWebhookNotifier
from config import EMAIL, PASSWORD, LOGIN_URL, TRACKING_LINKS_URL, HEADLESS, IMPLICIT_WAIT, PAGE_LOAD_TIMEOUT, SLACK_BOT_TOKEN, SLACK_CHANNEL_ID


class OnlyMonsterScraper:
    def __init__(self):
        self.driver = None
        self.db = TrackingDatabase()
        self.setup_driver()
    
    def setup_driver(self):
        """Initialize Chrome WebDriver with appropriate options"""
        chrome_options = Options()

        # Always run headless in server environment
        chrome_options.add_argument("--headless=new")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--allow-running-insecure-content")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

        try:
            # Try to use system chromedriver first
            service = Service("/usr/bin/chromedriver")
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
        except Exception as e1:
            print(f"System chromedriver failed: {e1}")
            # Fallback to ChromeDriverManager
            try:
                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            except Exception as e2:
                print(f"ChromeDriverManager also failed: {e2}")
                raise Exception(f"Failed to initialize Chrome WebDriver. System: {e1}, Manager: {e2}")

        self.driver.implicitly_wait(IMPLICIT_WAIT)
        self.driver.set_page_load_timeout(PAGE_LOAD_TIMEOUT)
    
    def login(self):
        """Login to OnlyMonster.ai with two-step process"""
        print("Navigating to OnlyMonster.ai sign-in page...")
        self.driver.get(LOGIN_URL)

        try:
            # Step 1: Enter email and click Next
            print("Step 1: Entering email...")
            email_input = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='email'], input[name='email'], input[placeholder*='email' i]"))
            )
            email_input.clear()
            email_input.send_keys(EMAIL)
            print("Email entered successfully")

            # Find and click Next button
            # Try multiple selectors for the Next button
            next_button = None
            possible_selectors = [
                "button[type='submit']",
                "input[type='submit']",
                "button",
                "[role='button']"
            ]

            for selector in possible_selectors:
                try:
                    buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for button in buttons:
                        button_text = button.text.lower()
                        if any(word in button_text for word in ['next', 'continue', 'submit']):
                            next_button = button
                            break
                    if next_button:
                        break
                except:
                    continue

            if not next_button:
                # Fallback: just click the first submit button
                next_button = WebDriverWait(self.driver, 10).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, "button[type='submit'], input[type='submit']"))
                )

            next_button.click()
            print("Next button clicked")

            # Step 2: Wait for password field and enter password
            print("Step 2: Waiting for password field...")
            time.sleep(2)  # Give the page time to load the password field

            password_input = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='password'], input[name='password']"))
            )
            password_input.clear()
            password_input.send_keys(PASSWORD)
            print("Password entered successfully")

            # Find and click the Continue button after password entry
            print("Looking for Continue button...")
            login_button = None

            # Try specific selectors for the Continue button
            continue_selectors = [
                ".cl-formButtonPrimary",  # Specific class from the HTML
                "button[data-localization-key='formButtonPrimary']",  # Specific attribute
                "button[type='submit']",
                "input[type='submit']"
            ]

            for selector in continue_selectors:
                try:
                    login_button = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    print(f"Found Continue button with selector: {selector}")
                    break
                except:
                    continue

            if not login_button:
                # Fallback: look for any button with "continue" text
                try:
                    buttons = self.driver.find_elements(By.TAG_NAME, "button")
                    for button in buttons:
                        if button.text.lower().strip() in ['continue', 'login', 'sign in', 'submit']:
                            login_button = button
                            print(f"Found button with text: {button.text}")
                            break
                except:
                    pass

            if not login_button:
                raise Exception("Could not find Continue/Login button")

            login_button.click()
            print("Continue button clicked")

            # Wait for successful login (check for dashboard or redirect)
            print("Waiting for redirect to panel...")
            try:
                WebDriverWait(self.driver, 20).until(
                    lambda driver: "panel" in driver.current_url.lower() or "dashboard" in driver.current_url.lower()
                )
                print("Login successful!")
            except TimeoutException:
                print(f"Redirect timeout. Current URL: {self.driver.current_url}")
                # Check if we're still on auth page or if there's an error
                if "auth" in self.driver.current_url:
                    print("Still on auth page, checking for errors...")
                    # Look for error messages
                    try:
                        error_elements = self.driver.find_elements(By.CSS_SELECTOR, "[role='alert'], .error, .cl-formFieldError")
                        if error_elements:
                            for error in error_elements:
                                if error.text.strip():
                                    print(f"Error found: {error.text}")
                    except:
                        pass
                raise

        except TimeoutException:
            print("Login failed - timeout waiting for elements")
            # Save page source for debugging
            with open("login_debug.html", "w", encoding="utf-8") as f:
                f.write(self.driver.page_source)
            print("Page source saved to login_debug.html for debugging")
            raise
        except NoSuchElementException as e:
            print(f"Login failed - element not found: {e}")
            # Save page source for debugging
            with open("login_debug.html", "w", encoding="utf-8") as f:
                f.write(self.driver.page_source)
            print("Page source saved to login_debug.html for debugging")
            raise
    
    def navigate_to_tracking_links(self):
        """Navigate to the tracking links page"""
        print("Navigating to tracking links page...")
        self.driver.get(TRACKING_LINKS_URL)
        
        # Wait for page to load
        WebDriverWait(self.driver, 15).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        time.sleep(3)  # Additional wait for dynamic content
        print("Tracking links page loaded")
    
    def extract_tracking_data(self):
        """Extract tracking links data from the page"""
        print("Extracting tracking data...")
        tracking_data = []
        
        try:
            # Common selectors for tables and data rows
            possible_selectors = [
                "table tbody tr",
                ".tracking-link",
                ".link-item",
                "[data-tracking]",
                ".table-row",
                "tr:has(td)"
            ]
            
            rows = None
            for selector in possible_selectors:
                try:
                    rows = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if rows:
                        print(f"Found {len(rows)} rows using selector: {selector}")
                        break
                except:
                    continue
            
            if not rows:
                # Fallback: look for any elements containing numbers that might be clicks/fans
                print("Trying fallback method to find data...")
                page_source = self.driver.page_source
                print("Page source length:", len(page_source))
                
                # Save page source for debugging
                with open("page_source.html", "w", encoding="utf-8") as f:
                    f.write(page_source)
                print("Page source saved to page_source.html for debugging")
                
                return tracking_data
            
            for row in rows:
                try:
                    # Extract text from the row
                    row_text = row.text.strip()
                    if not row_text:
                        continue
                    
                    # Look for patterns that might contain tracking link name, clicks, and fans
                    # This is a flexible approach that can be adjusted based on actual page structure
                    cells = row.find_elements(By.TAG_NAME, "td")
                    if not cells:
                        cells = row.find_elements(By.CSS_SELECTOR, "div, span")
                    
                    if len(cells) >= 3:
                        # Assume first cell is name, and look for numbers in other cells
                        raw_name = cells[0].text.strip()

                        # Clean up the name - remove "Created XX-XX-XXXX" part
                        name = raw_name.split('\nCreated')[0].strip()
                        if not name:
                            name = raw_name.split('Created')[0].strip()

                        clicks = 0
                        fans = 0
                        earnings = 0.00

                        # Extract numeric data from cells
                        # Expected order: Name, Clicks, Fans, Earnings
                        for i, cell in enumerate(cells[1:], 1):
                            cell_text = cell.text.strip()

                            # Look for earnings (contains $ symbol)
                            if '$' in cell_text:
                                # Extract earnings value - look for dollar amounts like $630.40, $283.11
                                earnings_match = re.search(r'\$[\d,]+\.?\d*', cell_text)
                                if earnings_match:
                                    try:
                                        # Remove $ and commas, convert to float
                                        earnings_str = earnings_match.group().replace('$', '').replace(',', '')
                                        earnings = float(earnings_str)
                                    except ValueError:
                                        earnings = 0.00
                            elif '%' not in cell_text:
                                # Extract integer values (clicks, fans) - avoid percentage values
                                numbers = re.findall(r'\d+', cell_text)
                                if numbers:
                                    num = int(numbers[0])
                                    if clicks == 0:
                                        clicks = num
                                    elif fans == 0:
                                        fans = num

                        if name and (clicks > 0 or fans > 0 or earnings > 0):
                            tracking_data.append((name, clicks, fans, earnings))
                            print(f"Extracted: {name} - Clicks: {clicks}, Fans: {fans}, Earnings: ${earnings:.2f}")
                
                except Exception as e:
                    print(f"Error processing row: {e}")
                    continue
            
            print(f"Successfully extracted {len(tracking_data)} tracking links")
            return tracking_data
            
        except Exception as e:
            print(f"Error extracting tracking data: {e}")
            return tracking_data
    
    def run_scraping(self):
        """Main method to run the complete scraping process"""
        try:
            print("Starting OnlyMonster scraping...")
            
            # Login
            self.login()
            
            # Navigate to tracking links
            self.navigate_to_tracking_links()
            
            # Extract data
            tracking_data = self.extract_tracking_data()
            
            if tracking_data:
                # Store in database
                self.db.insert_tracking_data(tracking_data)
                print(f"Successfully stored {len(tracking_data)} records in database")
            else:
                print("No tracking data found to store")
            
            return tracking_data
            
        except Exception as e:
            print(f"Scraping failed: {e}")
            raise
        finally:
            if self.driver:
                self.driver.quit()
                print("Browser closed")


if __name__ == "__main__":
    scraper = OnlyMonsterScraper()
    try:
        data = scraper.run_scraping()
        print("Scraping completed successfully!")

        # Run analytics after successful scraping
        if data:
            print("\n" + "="*60)
            print("🔍 RUNNING ANALYTICS...")
            print("="*60)

            analytics = TrackingAnalytics()
            analysis = analytics.run_analysis(24)  # Compare with 24 hours ago
            analytics.print_analysis_report(analysis)
        else:
            print("No data collected, skipping analysis.")

    except Exception as e:
        print(f"Scraping failed: {e}")
