# OnlyMonster.ai Tracking Links Automation

This automation script logs into OnlyMonster.ai, navigates to the tracking links page, and extracts tracking data (clicks, fans, link names) to store in a SQLite database with timestamps.

## Features

- Automated login to OnlyMonster.ai
- Navigation to tracking links page
- Data extraction of tracking link names, clicks, and fans
- **💰 Revenue tracking and management** with web interface
- SQLite database storage with timestamps
- **AI-powered analytics using Claude Sonnet 4 thinking** for intelligent insights and recommendations
- **Automated Slack reporting** with AI suggestions included
- **Web-based revenue tracking dashboard** for manual revenue entry and analysis
- Configurable headless/visible browser mode
- Error handling and logging

## Setup

1. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Install Chrome browser** (if not already installed)
   - The script uses Chrome WebDriver which will be automatically downloaded

3. **Configuration:**
   - Credentials are stored in `config.py`
   - **OpenRouter API Key**: Required for DeepSeek R1 AI analysis
   - **Slack Bot Token & Channel ID**: Required for automated Slack reporting
   - You can modify settings like headless mode, timeouts, etc.

4. **AI & Slack Setup:**
   ```python
   # In config.py, configure these settings:
   OPENROUTER_API_KEY = "your-openrouter-api-key"  # For Claude Sonnet 4 thinking AI analysis
   SLACK_BOT_TOKEN = "xoxb-your-slack-bot-token"   # For Slack notifications
   SLACK_CHANNEL_ID = "your-channel-id"            # Target Slack channel
   ```

## Usage

### Run the scraper:
```bash
python onlymonster_scraper.py
```

### Run AI Analytics with Slack Reporting:
```bash
# Generate daily report with AI insights and send to Slack
python daily_report_with_slack.py

# Fresh scrape + immediate analysis + Slack notification
python scrape_and_analyze.py

# Standardized daily report format
python standardized_daily_report.py

# Run analytics only (no Slack)
python run_analytics.py [hours_back]
```

### Revenue Tracking:
```bash
# Start the revenue tracking web interface
python revenue_tracker.py

# Test revenue tracking functionality
python test_revenue_tracking.py

# Access dashboard at: http://localhost:5000
```

### Start Automated Daily Reports:
```bash
# Run daily reports automatically at 9:00 AM
python daily_scheduler.py
```

### View stored data:
```python
from database import TrackingDatabase

db = TrackingDatabase()

# Get latest 10 records
recent_data = db.get_latest_data(10)
for name, clicks, fans, timestamp in recent_data:
    print(f"{name}: {clicks} clicks, {fans} fans ({timestamp})")

# Get data for specific date
today_data = db.get_data_by_date("2024-06-08")
```

## Database Schema

The SQLite database (`onlymonster_data.db`) contains a table `tracking_data` with:
- `id`: Auto-incrementing primary key
- `tracking_link_name`: Name of the tracking link
- `clicks`: Number of clicks
- `fans`: Number of fans
- `timestamp`: When the data was scraped
- `created_at`: Database record creation time

## AI Analytics & Slack Integration

### AI-Powered Analysis
- **Model**: Claude Sonnet 4 thinking (anthropic/claude-3.7-sonnet:thinking) via OpenRouter API
- **Features**: Advanced reasoning capabilities, intelligent performance analysis, conversion rate insights, strategic recommendations
- **Output**: Actionable suggestions for scaling successful campaigns and optimizing underperforming links

### Slack Reporting
- **Automated notifications** sent to configured Slack channel
- **AI suggestions included** in all reports for immediate actionability
- **Real-time alerts** for performance changes and optimization opportunities
- **Formatted reports** with emojis and clear metrics for quick scanning

### Available Scripts
- `daily_report_with_slack.py`: Generate comprehensive daily report with AI insights
- `scrape_and_analyze.py`: Fresh scrape + immediate analysis + Slack notification
- `standardized_daily_report.py`: Standardized reporting format with AI suggestions
- `daily_scheduler.py`: Automated daily reports at scheduled times

## Files

- `onlymonster_scraper.py`: Main scraping script
- `analytics.py`: AI-powered analytics engine using Claude Sonnet 4 thinking
- `database.py`: Database operations with revenue tracking
- `revenue_tracker.py`: Web interface for revenue tracking and management
- `config.py`: Configuration and credentials (includes OpenRouter API key and Slack tokens)
- `slack_webhook.py`: Slack integration and message formatting
- `test_revenue_tracking.py`: Test script for revenue functionality
- `templates/`: HTML templates for the web interface
- `requirements.txt`: Python dependencies
- `README.md`: This file
- `REVENUE_TRACKING.md`: Detailed revenue tracking documentation

## Troubleshooting

1. **Login Issues:**
   - Check credentials in `config.py`
   - Verify OnlyMonster.ai site structure hasn't changed
   - Check if CAPTCHA or 2FA is required

2. **Data Extraction Issues:**
   - The script saves `page_source.html` for debugging
   - Check if page structure has changed
   - Modify selectors in `extract_tracking_data()` method

3. **Browser Issues:**
   - Set `HEADLESS = False` in config.py to see browser actions
   - Check Chrome browser installation
   - Update Chrome WebDriver if needed

## Scheduling

To run this automation regularly, you can:

1. **Use cron (Linux/Mac):**
   ```bash
   # Run every hour
   0 * * * * cd /path/to/project && python onlymonster_scraper.py
   ```

2. **Use Task Scheduler (Windows)**

3. **Use a Python scheduler:**
   ```python
   import schedule
   import time
   
   schedule.every().hour.do(scraper.run_scraping)
   
   while True:
       schedule.run_pending()
       time.sleep(1)
   ```

## Security Notes

- Credentials are stored in plain text in `config.py`
- Consider using environment variables for production
- Be mindful of rate limiting and terms of service
