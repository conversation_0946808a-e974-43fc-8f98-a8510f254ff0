"""
Analytics module for OnlyMonster tracking data analysis
"""
import sqlite3
import json
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from config import DATABASE_PATH, OPENROUTER_API_KEY, PRIORITY_LINKS
from link_combiner import <PERSON><PERSON><PERSON><PERSON>


class TrackingAnalytics:
    def __init__(self):
        self.db_path = DATABASE_PATH
        self.api_key = OPENROUTER_API_KEY
        self.priority_links = PRIORITY_LINKS
        self.link_combiner = LinkCombiner()
    
    def get_latest_data(self) -> List[Tuple]:
        """Get the most recent data for each tracking link with combinations applied"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT tracking_link_name, clicks, fans, earnings, revenue, timestamp
                FROM tracking_data t1
                WHERE timestamp = (
                    SELECT MAX(timestamp)
                    FROM tracking_data t2
                    WHERE t2.tracking_link_name = t1.tracking_link_name
                )
                ORDER BY tracking_link_name
            ''')
            raw_data = cursor.fetchall()
            return self.link_combiner.combine_latest_data(raw_data)
    
    def get_previous_data(self, hours_back: int = 24) -> List[Tuple]:
        """Get data from specified hours ago with combinations applied"""
        cutoff_time = datetime.now() - timedelta(hours=hours_back)

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT tracking_link_name, clicks, fans, earnings, revenue, timestamp
                FROM tracking_data
                WHERE timestamp <= ?
                ORDER BY tracking_link_name, timestamp DESC
            ''', (cutoff_time,))

            # Get the most recent record for each link before the cutoff
            results = {}
            for row in cursor.fetchall():
                if len(row) == 4:
                    name, clicks, fans, timestamp = row
                    earnings = 0.00
                    revenue = 0.00
                elif len(row) == 5:
                    name, clicks, fans, earnings, timestamp = row
                    revenue = 0.00
                elif len(row) == 6:
                    name, clicks, fans, earnings, revenue, timestamp = row
                else:
                    continue

                if name not in results:
                    results[name] = (name, clicks, fans, earnings, revenue, timestamp)

            raw_data = list(results.values())
            return self.link_combiner.combine_latest_data(raw_data)
    
    def calculate_changes(self, current_data: List[Tuple], previous_data: List[Tuple]) -> Dict:
        """Calculate changes between current and previous data"""
        # Handle multiple data formats
        current_dict = {}
        for item in current_data:
            if len(item) == 4:
                name, clicks, fans, timestamp = item
                earnings = 0.00
                revenue = 0.00
            elif len(item) == 5:
                name, clicks, fans, earnings, timestamp = item
                revenue = 0.00
            elif len(item) == 6:
                name, clicks, fans, earnings, revenue, timestamp = item
            else:
                continue
            current_dict[name] = (clicks, fans, earnings, revenue)

        previous_dict = {}
        for item in previous_data:
            if len(item) == 4:
                name, clicks, fans, timestamp = item
                earnings = 0.00
                revenue = 0.00
            elif len(item) == 5:
                name, clicks, fans, earnings, timestamp = item
                revenue = 0.00
            elif len(item) == 6:
                name, clicks, fans, earnings, revenue, timestamp = item
            else:
                continue
            previous_dict[name] = (clicks, fans, earnings, revenue)

        changes = {
            'new_clicks': {},
            'new_fans': {},
            'new_earnings': {},
            'new_revenue': {},
            'conversion_rates': {},
            'earnings_per_fan': {},
            'revenue_per_fan': {},
            'no_updates': [],
            'priority_changes': {}
        }
        
        for name in current_dict:
            current_clicks, current_fans, current_earnings, current_revenue = current_dict[name]

            if name in previous_dict:
                prev_clicks, prev_fans, prev_earnings, prev_revenue = previous_dict[name]

                # Calculate changes
                click_change = current_clicks - prev_clicks
                fan_change = current_fans - prev_fans
                earnings_change = current_earnings - prev_earnings
                revenue_change = current_revenue - prev_revenue

                # Calculate conversion rates
                current_rate = (current_fans / current_clicks * 100) if current_clicks > 0 else 0
                prev_rate = (prev_fans / prev_clicks * 100) if prev_clicks > 0 else 0
                rate_change = current_rate - prev_rate

                # Calculate earnings per fan
                current_epf = (current_earnings / current_fans) if current_fans > 0 else 0
                prev_epf = (prev_earnings / prev_fans) if prev_fans > 0 else 0
                epf_change = current_epf - prev_epf

                # Calculate revenue per fan
                current_rpf = (current_revenue / current_fans) if current_fans > 0 else 0
                prev_rpf = (prev_revenue / prev_fans) if prev_fans > 0 else 0
                rpf_change = current_rpf - prev_rpf

                changes['new_clicks'][name] = click_change
                changes['new_fans'][name] = fan_change
                changes['new_earnings'][name] = earnings_change
                changes['new_revenue'][name] = revenue_change
                changes['conversion_rates'][name] = {
                    'current': current_rate,
                    'previous': prev_rate,
                    'change': rate_change
                }
                changes['earnings_per_fan'][name] = {
                    'current': current_epf,
                    'previous': prev_epf,
                    'change': epf_change
                }
                changes['revenue_per_fan'][name] = {
                    'current': current_rpf,
                    'previous': prev_rpf,
                    'change': rpf_change
                }

                # Check for no updates
                if click_change == 0 and fan_change == 0:
                    changes['no_updates'].append(name)

                # Track priority links
                if name in self.priority_links:
                    changes['priority_changes'][name] = {
                        'clicks_change': click_change,
                        'fans_change': fan_change,
                        'earnings_change': earnings_change,
                        'revenue_change': revenue_change,
                        'conversion_rate_change': rate_change,
                        'earnings_per_fan_change': epf_change,
                        'revenue_per_fan_change': rpf_change,
                        'current_clicks': current_clicks,
                        'current_fans': current_fans,
                        'current_earnings': current_earnings,
                        'current_revenue': current_revenue,
                        'current_rate': current_rate,
                        'current_epf': current_epf,
                        'current_rpf': current_rpf
                    }
            else:
                # New tracking link
                changes['new_clicks'][name] = current_clicks
                changes['new_fans'][name] = current_fans
                changes['new_earnings'][name] = current_earnings
                changes['new_revenue'][name] = current_revenue
                current_rate = (current_fans / current_clicks * 100) if current_clicks > 0 else 0
                current_epf = (current_earnings / current_fans) if current_fans > 0 else 0
                current_rpf = (current_revenue / current_fans) if current_fans > 0 else 0
                changes['conversion_rates'][name] = {
                    'current': current_rate,
                    'previous': 0,
                    'change': current_rate
                }
                changes['earnings_per_fan'][name] = {
                    'current': current_epf,
                    'previous': 0,
                    'change': current_epf
                }
                changes['revenue_per_fan'][name] = {
                    'current': current_rpf,
                    'previous': 0,
                    'change': current_rpf
                }
        
        return changes
    
    def call_openrouter_api(self, prompt: str) -> str:
        """Call OpenRouter API for AI analysis"""
        url = "https://openrouter.ai/api/v1/chat/completions"
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "anthropic/claude-3.7-sonnet:thinking",
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "max_tokens": 1500,
            "temperature": 0.7
        }
        
        try:
            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()
            result = response.json()
            return result['choices'][0]['message']['content']
        except Exception as e:
            return f"AI Analysis Error: {str(e)}"
    
    def generate_analysis_prompt(self, changes: Dict) -> str:
        """Generate a prompt for AI analysis"""
        prompt = f"""
Analyze the following OnlyMonster tracking data changes and provide insights:

PRIORITY TRACKING LINKS PERFORMANCE:
{json.dumps(changes['priority_changes'], indent=2)}

ALL CHANGES SUMMARY:
- New Clicks by Source: {dict(sorted(changes['new_clicks'].items(), key=lambda x: x[1], reverse=True))}
- New Fans by Source: {dict(sorted(changes['new_fans'].items(), key=lambda x: x[1], reverse=True))}
- New Earnings by Source: {dict(sorted(changes['new_earnings'].items(), key=lambda x: x[1], reverse=True))}
- New Revenue by Source: {dict(sorted(changes['new_revenue'].items(), key=lambda x: x[1], reverse=True))}
- Links with No Updates: {changes['no_updates']}

CONVERSION RATE CHANGES:
{json.dumps({k: v for k, v in changes['conversion_rates'].items() if abs(v['change']) > 0.5}, indent=2)}

EARNINGS PER FAN ANALYSIS:
{json.dumps({k: v for k, v in changes['earnings_per_fan'].items() if v['current'] > 0}, indent=2)}

REVENUE PER FAN ANALYSIS:
{json.dumps({k: v for k, v in changes['revenue_per_fan'].items() if v['current'] > 0}, indent=2)}

Please provide:
1. **Top Performing Sources**: Which tracking links brought in the most new fans, clicks, and earnings
2. **Earnings Analysis**: Which platforms generate the highest earnings per fan from OnlyMonster
3. **Zero Earnings Platforms**: Which platforms are bringing fans but no earnings - identify underperforming sources
4. **Conversion Analysis**: Which sources have the best/worst conversion rates and significant changes
5. **ROI Insights**: Analysis of earnings efficiency and which platforms provide best return per fan
6. **Monetization Gaps**: Identify platforms with high fan counts but low earnings per fan
7. **Strategic Recommendations**: Focus on earnings optimization and platform performance
8. **Priority Links Focus**: Specific earnings insights on the 7 priority tracking links

Focus on earnings performance and identifying which platforms are most/least profitable per fan acquired.

Keep the analysis concise but actionable. Focus on business insights and recommendations.
"""
        return prompt
    
    def run_analysis(self, hours_back: int = 24) -> Dict:
        """Run complete analysis comparing current vs previous data"""
        print(f"🔍 Running analysis comparing current data vs {hours_back} hours ago...")
        
        # Get data
        current_data = self.get_latest_data()
        previous_data = self.get_previous_data(hours_back)
        
        print(f"📊 Current data points: {len(current_data)}")
        print(f"📊 Previous data points: {len(previous_data)}")
        
        # Calculate changes
        changes = self.calculate_changes(current_data, previous_data)
        
        # Generate AI analysis
        print("🤖 Generating AI analysis...")
        prompt = self.generate_analysis_prompt(changes)
        ai_analysis = self.call_openrouter_api(prompt)
        
        return {
            'changes': changes,
            'ai_analysis': ai_analysis,
            'current_data': current_data,
            'previous_data': previous_data
        }
    
    def print_analysis_report(self, analysis: Dict):
        """Print a formatted analysis report"""
        changes = analysis['changes']
        
        print("\n" + "="*80)
        print("🎯 ONLYMONSTER TRACKING ANALYSIS REPORT")
        print("="*80)
        
        # Priority links summary
        print("\n🔥 PRIORITY LINKS PERFORMANCE:")
        print("-" * 50)
        for link, data in changes['priority_changes'].items():
            print(f"📈 {link}:")
            print(f"   Clicks: +{data['clicks_change']:,} (Total: {data['current_clicks']:,})")
            print(f"   Fans: +{data['fans_change']} (Total: {data['current_fans']})")
            print(f"   Earnings: +${data.get('earnings_change', 0):.2f} (Total: ${data.get('current_earnings', 0):.2f})")
            print(f"   Revenue: +${data.get('revenue_change', 0):.2f} (Total: ${data.get('current_revenue', 0):.2f})")
            print(f"   Conversion: {data['current_rate']:.2f}% ({data['conversion_rate_change']:+.2f}%)")
            if data.get('current_epf', 0) > 0:
                print(f"   Earnings/Fan: ${data.get('current_epf', 0):.2f} ({data.get('earnings_per_fan_change', 0):+.2f})")
            if data.get('current_rpf', 0) > 0:
                print(f"   Revenue/Fan: ${data.get('current_rpf', 0):.2f} ({data.get('revenue_per_fan_change', 0):+.2f})")
            print()
        
        # Top performers
        print("🏆 TOP CLICK SOURCES (New Clicks):")
        top_clicks = sorted(changes['new_clicks'].items(), key=lambda x: x[1], reverse=True)[:5]
        for link, clicks in top_clicks:
            if clicks > 0:
                print(f"   {link}: +{clicks:,} clicks")
        
        print("\n👥 TOP FAN SOURCES (New Fans):")
        top_fans = sorted(changes['new_fans'].items(), key=lambda x: x[1], reverse=True)[:5]
        for link, fans in top_fans:
            if fans > 0:
                print(f"   {link}: +{fans} fans")

        print("\n💰 TOP EARNINGS SOURCES (OnlyMonster Platform):")
        top_earnings = sorted(changes['new_earnings'].items(), key=lambda x: x[1], reverse=True)[:5]
        for link, earnings in top_earnings:
            if earnings > 0:
                print(f"   {link}: +${earnings:.2f}")

        print("\n� HIGHEST EARNINGS PER FAN:")
        top_epf = sorted(changes['earnings_per_fan'].items(), key=lambda x: x[1]['current'], reverse=True)[:5]
        for link, epf_data in top_epf:
            if epf_data['current'] > 0:
                print(f"   {link}: ${epf_data['current']:.2f}/fan")

        print("\n⚠️  ZERO EARNINGS PLATFORMS:")
        zero_earnings = [link for link, epf_data in changes['earnings_per_fan'].items()
                        if epf_data['current'] == 0 and link in changes['new_fans'] and changes['new_fans'][link] > 0]
        if zero_earnings:
            for link in zero_earnings[:5]:
                fans = changes['new_fans'].get(link, 0)
                print(f"   {link}: {fans} fans but $0.00 earnings")
        else:
            print("   All active platforms are generating earnings!")
        
        # Stagnant links
        if changes['no_updates']:
            print(f"\n⚠️  STAGNANT LINKS ({len(changes['no_updates'])} links with no updates):")
            for link in changes['no_updates']:
                print(f"   - {link}")
        
        # AI Analysis
        print("\n🤖 AI ANALYSIS & RECOMMENDATIONS:")
        print("-" * 50)
        print(analysis['ai_analysis'])
        
        print("\n" + "="*80)


if __name__ == "__main__":
    analytics = TrackingAnalytics()
    analysis = analytics.run_analysis(24)  # Compare with 24 hours ago
    analytics.print_analysis_report(analysis)
