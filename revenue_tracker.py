#!/usr/bin/env python3
"""
OnlyMonster Revenue Tracking Web Interface
Simple Flask app for manually entering and tracking revenue data
"""
from flask import Flask, render_template, request, jsonify, redirect, url_for
from database import TrackingDatabase
from datetime import datetime, timedelta
import json

app = Flask(__name__)
app.secret_key = 'onlymonster-revenue-tracker-2024'

# Initialize database
db = TrackingDatabase()

@app.route('/')
def index():
    """Main dashboard showing recent tracking data with revenue"""
    try:
        # Get latest data for all tracking links
        latest_data = db.get_latest_data(50)
        
        # Group by tracking link name (get most recent for each)
        links_data = {}
        for name, clicks, fans, revenue, timestamp in latest_data:
            if name not in links_data:
                links_data[name] = {
                    'name': name,
                    'clicks': clicks,
                    'fans': fans,
                    'revenue': float(revenue) if revenue else 0.00,
                    'timestamp': timestamp,
                    'conversion_rate': (fans / clicks * 100) if clicks > 0 else 0
                }
        
        # Convert to list and sort by revenue
        links_list = list(links_data.values())
        links_list.sort(key=lambda x: x['revenue'], reverse=True)
        
        # Get revenue summary
        revenue_summary = db.get_revenue_summary(7)
        
        return render_template('dashboard.html', 
                             links=links_list, 
                             revenue_summary=revenue_summary)
    except Exception as e:
        return f"Error loading dashboard: {e}", 500

@app.route('/update_revenue', methods=['POST'])
def update_revenue():
    """Update revenue for a specific tracking link"""
    try:
        data = request.get_json()
        link_name = data.get('link_name')
        revenue = float(data.get('revenue', 0))
        timestamp = data.get('timestamp')
        
        if not link_name:
            return jsonify({'error': 'Link name is required'}), 400
        
        # Update revenue in database
        db.update_revenue(link_name, revenue, timestamp)
        
        return jsonify({'success': True, 'message': f'Revenue updated for {link_name}'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/revenue_history/<link_name>')
def revenue_history(link_name):
    """Show revenue history for a specific link"""
    try:
        # Get historical data for this link
        import sqlite3
        with sqlite3.connect(db.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT clicks, fans, revenue, timestamp
                FROM tracking_data
                WHERE tracking_link_name = ?
                ORDER BY timestamp DESC
                LIMIT 30
            ''', (link_name,))
            history = cursor.fetchall()
        
        history_data = []
        for clicks, fans, revenue, timestamp in history:
            history_data.append({
                'clicks': clicks,
                'fans': fans,
                'revenue': float(revenue) if revenue else 0.00,
                'timestamp': timestamp,
                'conversion_rate': (fans / clicks * 100) if clicks > 0 else 0
            })
        
        return render_template('revenue_history.html', 
                             link_name=link_name, 
                             history=history_data)
    except Exception as e:
        return f"Error loading history: {e}", 500

@app.route('/api/links')
def api_links():
    """API endpoint to get all tracking links with latest data"""
    try:
        latest_data = db.get_latest_data(100)
        
        links_data = {}
        for name, clicks, fans, revenue, timestamp in latest_data:
            if name not in links_data:
                links_data[name] = {
                    'name': name,
                    'clicks': clicks,
                    'fans': fans,
                    'revenue': float(revenue) if revenue else 0.00,
                    'timestamp': timestamp,
                    'conversion_rate': (fans / clicks * 100) if clicks > 0 else 0
                }
        
        return jsonify(list(links_data.values()))
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/revenue_summary')
def api_revenue_summary():
    """API endpoint for revenue summary"""
    try:
        summary = db.get_revenue_summary(30)  # Last 30 days
        
        summary_data = []
        for row in summary:
            summary_data.append({
                'link_name': row[0],
                'total_revenue': float(row[1]) if row[1] else 0.00,
                'data_points': row[2],
                'avg_revenue': float(row[3]) if row[3] else 0.00,
                'max_revenue': float(row[4]) if row[4] else 0.00,
                'first_date': row[5],
                'last_date': row[6]
            })
        
        return jsonify(summary_data)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    import os
    if not os.path.exists('templates'):
        os.makedirs('templates')
    
    print("🚀 Starting OnlyMonster Revenue Tracker")
    print("📊 Dashboard will be available at: http://localhost:5000")
    print("💰 Use this interface to track revenue for your OnlyMonster links")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
