#!/usr/bin/env python3
"""
Test script for revenue tracking functionality
"""
from database import TrackingDatabase
from analytics import TrackingAnalytics
import datetime

def test_revenue_functionality():
    """Test the revenue tracking features"""
    print("🧪 Testing OnlyMonster Revenue Tracking Functionality")
    print("="*60)
    
    # Initialize database
    db = TrackingDatabase()
    
    # Test 1: Insert sample data with revenue
    print("\n1️⃣ Testing data insertion with revenue...")
    sample_data = [
        ("test-link-1", 1000, 50, 125.50),  # name, clicks, fans, revenue
        ("test-link-2", 800, 32, 89.75),
        ("test-link-3", 1200, 48, 156.00)
    ]
    
    try:
        db.insert_tracking_data(sample_data)
        print("✅ Successfully inserted sample data with revenue")
    except Exception as e:
        print(f"❌ Error inserting data: {e}")
        return
    
    # Test 2: Retrieve latest data
    print("\n2️⃣ Testing data retrieval...")
    try:
        latest_data = db.get_latest_data(10)
        print(f"✅ Retrieved {len(latest_data)} records")
        for record in latest_data[-3:]:  # Show last 3
            if len(record) >= 5:
                name, clicks, fans, revenue, timestamp = record[:5]
                print(f"   {name}: {clicks} clicks, {fans} fans, ${revenue:.2f} revenue")
            else:
                print(f"   Old format record: {record}")
    except Exception as e:
        print(f"❌ Error retrieving data: {e}")
    
    # Test 3: Update revenue
    print("\n3️⃣ Testing revenue updates...")
    try:
        db.update_revenue("test-link-1", 200.00)
        print("✅ Successfully updated revenue for test-link-1")
    except Exception as e:
        print(f"❌ Error updating revenue: {e}")
    
    # Test 4: Revenue summary
    print("\n4️⃣ Testing revenue summary...")
    try:
        summary = db.get_revenue_summary(7)
        print(f"✅ Generated revenue summary for {len(summary)} links")
        for row in summary:
            if len(row) >= 7:
                link_name, total_rev, data_points, avg_rev, max_rev, first_date, last_date = row
                print(f"   {link_name}: Total ${total_rev:.2f}, Avg ${avg_rev:.2f}")
    except Exception as e:
        print(f"❌ Error generating summary: {e}")
    
    # Test 5: Analytics with revenue
    print("\n5️⃣ Testing analytics with revenue...")
    try:
        analytics = TrackingAnalytics()
        
        # Insert some older data for comparison
        older_data = [
            ("test-link-1", 900, 45, 100.00),
            ("test-link-2", 750, 30, 75.00),
            ("test-link-3", 1100, 44, 140.00)
        ]
        
        # Manually set older timestamp
        import sqlite3
        timestamp = datetime.datetime.now() - datetime.timedelta(hours=25)
        with sqlite3.connect(db.db_path) as conn:
            cursor = conn.cursor()
            cursor.executemany('''
                INSERT INTO tracking_data (tracking_link_name, clicks, fans, revenue, timestamp)
                VALUES (?, ?, ?, ?, ?)
            ''', [(name, clicks, fans, revenue, timestamp) for name, clicks, fans, revenue in older_data])
            conn.commit()
        
        print("✅ Inserted historical data for comparison")
        
        # Run analysis
        analysis = analytics.run_analysis(24)
        print("✅ Successfully ran analytics with revenue data")
        
        # Show some results
        changes = analysis['changes']
        print(f"\n📊 Revenue changes found for {len(changes['new_revenue'])} links:")
        for link, revenue_change in changes['new_revenue'].items():
            if revenue_change != 0:
                print(f"   {link}: ${revenue_change:+.2f}")
        
    except Exception as e:
        print(f"❌ Error in analytics: {e}")
    
    print("\n✅ Revenue tracking functionality test completed!")
    print("\n💡 Next steps:")
    print("   1. Run 'python revenue_tracker.py' to start the web interface")
    print("   2. Visit http://localhost:5000 to manage revenue data")
    print("   3. Use the web interface to update revenue for your tracking links")

def cleanup_test_data():
    """Clean up test data"""
    print("\n🧹 Cleaning up test data...")
    try:
        import sqlite3
        from config import DATABASE_PATH
        
        with sqlite3.connect(DATABASE_PATH) as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM tracking_data WHERE tracking_link_name LIKE 'test-link-%'")
            deleted = cursor.rowcount
            conn.commit()
        
        print(f"✅ Cleaned up {deleted} test records")
    except Exception as e:
        print(f"❌ Error cleaning up: {e}")

if __name__ == "__main__":
    test_revenue_functionality()
    
    # Ask user if they want to clean up test data
    response = input("\n🗑️  Clean up test data? (y/n): ").lower().strip()
    if response in ['y', 'yes']:
        cleanup_test_data()
    else:
        print("💾 Test data preserved for further testing")
