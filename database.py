"""
Database operations for OnlyMonster tracking data
"""
import sqlite3
import datetime
from typing import List, Tuple
from config import DATABASE_PATH


class TrackingDatabase:
    def __init__(self):
        self.db_path = DATABASE_PATH
        self.init_database()
    
    def init_database(self):
        """Initialize the database and create tables if they don't exist"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS tracking_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    tracking_link_name TEXT NOT NULL,
                    clicks INTEGER NOT NULL,
                    fans INTEGER NOT NULL,
                    earnings DECIMAL(10,2) DEFAULT 0.00,
                    revenue DECIMAL(10,2) DEFAULT 0.00,
                    timestamp DATETIME NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Check if revenue and earnings columns exist, if not add them
            cursor.execute("PRAGMA table_info(tracking_data)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'earnings' not in columns:
                cursor.execute('ALTER TABLE tracking_data ADD COLUMN earnings DECIMAL(10,2) DEFAULT 0.00')
                print("Added earnings column to existing tracking_data table")

            if 'revenue' not in columns:
                cursor.execute('ALTER TABLE tracking_data ADD COLUMN revenue DECIMAL(10,2) DEFAULT 0.00')
                print("Added revenue column to existing tracking_data table")

            conn.commit()
    
    def insert_tracking_data(self, data: List[Tuple]):
        """
        Insert tracking data into the database

        Args:
            data: List of tuples containing:
                  - (tracking_link_name, clicks, fans) - old format
                  - (tracking_link_name, clicks, fans, earnings) - with earnings from scraper
                  - (tracking_link_name, clicks, fans, earnings, revenue) - full format
        """
        timestamp = datetime.datetime.now()

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Prepare data with timestamp, handling multiple formats
            data_with_timestamp = []
            for item in data:
                if len(item) == 3:
                    # Old format: (name, clicks, fans)
                    name, clicks, fans = item
                    earnings = 0.00
                    revenue = 0.00
                elif len(item) == 4:
                    # New format with earnings: (name, clicks, fans, earnings)
                    name, clicks, fans, earnings = item
                    revenue = 0.00
                elif len(item) == 5:
                    # Full format: (name, clicks, fans, earnings, revenue)
                    name, clicks, fans, earnings, revenue = item
                else:
                    raise ValueError(f"Invalid data format: {item}")

                data_with_timestamp.append((name, clicks, fans, earnings, revenue, timestamp))

            cursor.executemany('''
                INSERT INTO tracking_data (tracking_link_name, clicks, fans, earnings, revenue, timestamp)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', data_with_timestamp)

            conn.commit()
            print(f"Inserted {len(data)} records at {timestamp}")
    
    def get_latest_data(self, limit: int = 10):
        """Get the most recent tracking data"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT tracking_link_name, clicks, fans, earnings, revenue, timestamp
                FROM tracking_data
                ORDER BY timestamp DESC
                LIMIT ?
            ''', (limit,))
            return cursor.fetchall()

    def get_data_by_date(self, date: str):
        """Get tracking data for a specific date (YYYY-MM-DD format)"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT tracking_link_name, clicks, fans, earnings, revenue, timestamp
                FROM tracking_data
                WHERE DATE(timestamp) = ?
                ORDER BY timestamp DESC
            ''', (date,))
            return cursor.fetchall()

    def get_data_by_timeframe(self, hours_back: int):
        """Get tracking data from X hours ago"""
        cutoff_time = datetime.datetime.now() - datetime.timedelta(hours=hours_back)

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT tracking_link_name, clicks, fans, earnings, revenue, timestamp
                FROM tracking_data
                WHERE timestamp >= ?
                ORDER BY timestamp DESC
            ''', (cutoff_time,))
            return cursor.fetchall()

    def update_revenue(self, tracking_link_name: str, revenue: float, timestamp: str = None):
        """
        Update revenue for a specific tracking link and timestamp

        Args:
            tracking_link_name: Name of the tracking link
            revenue: Revenue amount to set
            timestamp: Specific timestamp (optional, uses latest if not provided)
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            if timestamp:
                cursor.execute('''
                    UPDATE tracking_data
                    SET revenue = ?
                    WHERE tracking_link_name = ? AND timestamp = ?
                ''', (revenue, tracking_link_name, timestamp))
            else:
                # Update the most recent record for this link
                cursor.execute('''
                    UPDATE tracking_data
                    SET revenue = ?
                    WHERE tracking_link_name = ? AND timestamp = (
                        SELECT MAX(timestamp)
                        FROM tracking_data
                        WHERE tracking_link_name = ?
                    )
                ''', (revenue, tracking_link_name, tracking_link_name))

            conn.commit()
            print(f"Updated revenue for {tracking_link_name}: ${revenue:.2f}")

    def update_earnings(self, tracking_link_name: str, earnings: float, timestamp: str = None):
        """
        Update earnings for a specific tracking link and timestamp

        Args:
            tracking_link_name: Name of the tracking link
            earnings: Earnings amount to set
            timestamp: Specific timestamp (optional, uses latest if not provided)
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            if timestamp:
                cursor.execute('''
                    UPDATE tracking_data
                    SET earnings = ?
                    WHERE tracking_link_name = ? AND timestamp = ?
                ''', (earnings, tracking_link_name, timestamp))
            else:
                # Update the most recent record for this link
                cursor.execute('''
                    UPDATE tracking_data
                    SET earnings = ?
                    WHERE tracking_link_name = ? AND timestamp = (
                        SELECT MAX(timestamp)
                        FROM tracking_data
                        WHERE tracking_link_name = ?
                    )
                ''', (earnings, tracking_link_name, tracking_link_name))

            conn.commit()
            print(f"Updated earnings for {tracking_link_name}: ${earnings:.2f}")

    def get_revenue_summary(self, days_back: int = 7):
        """Get revenue summary for the last N days"""
        cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days_back)

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT
                    tracking_link_name,
                    SUM(revenue) as total_revenue,
                    COUNT(*) as data_points,
                    AVG(revenue) as avg_revenue,
                    MAX(revenue) as max_revenue,
                    MIN(timestamp) as first_date,
                    MAX(timestamp) as last_date
                FROM tracking_data
                WHERE timestamp >= ?
                GROUP BY tracking_link_name
                ORDER BY total_revenue DESC
            ''', (cutoff_date,))
            return cursor.fetchall()

    def get_earnings_summary(self, days_back: int = 7):
        """Get earnings summary for the last N days"""
        cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days_back)

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT
                    tracking_link_name,
                    SUM(earnings) as total_earnings,
                    COUNT(*) as data_points,
                    AVG(earnings) as avg_earnings,
                    MAX(earnings) as max_earnings,
                    MIN(timestamp) as first_date,
                    MAX(timestamp) as last_date
                FROM tracking_data
                WHERE timestamp >= ?
                GROUP BY tracking_link_name
                ORDER BY total_earnings DESC
            ''', (cutoff_date,))
            return cursor.fetchall()
